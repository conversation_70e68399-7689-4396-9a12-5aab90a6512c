import React, { useEffect, useRef } from 'react'
import { Box } from '@mui/material'
import { keyframes } from '@emotion/react'

// Interactive background animations
const float = keyframes`
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
`

const rotate = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`

const bounce = keyframes`
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
`

const particleMove = keyframes`
  0% { transform: translateY(100vh) translateX(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-100vh) translateX(100px); opacity: 0; }
`

const rippleAnimation = keyframes`
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
`

const waveAnimation = keyframes`
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(-10px); }
`

const particleBurst = keyframes`
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(calc(-50% + var(--end-x)), calc(-50% + var(--end-y))) scale(0);
    opacity: 0;
  }
`

export const InteractiveBackground: React.FC = () => {
  const backgroundRef = useRef<HTMLDivElement>(null)

  // Interactive background effects
  useEffect(() => {
    const createParticle = () => {
      if (!backgroundRef.current) return

      const particle = document.createElement('div')
      particle.style.position = 'absolute'
      particle.style.width = '4px'
      particle.style.height = '4px'
      particle.style.background = 'rgba(102, 126, 234, 0.3)'
      particle.style.borderRadius = '50%'
      particle.style.left = Math.random() * 100 + '%'
      particle.style.animation = `${particleMove} ${Math.random() * 10 + 10}s linear infinite`
      particle.style.animationDelay = Math.random() * 2 + 's'
      particle.style.zIndex = '5'

      backgroundRef.current.appendChild(particle)

      setTimeout(() => {
        if (particle.parentNode) {
          particle.parentNode.removeChild(particle)
        }
      }, 20000)
    }

    // Create initial particles
    for (let i = 0; i < 3; i++) {
      setTimeout(createParticle, i * 500)
    }

    const interval = setInterval(createParticle, 1000) // Daha sık particle
    return () => clearInterval(interval)
  }, [])

  // Ripple effect on click
  const handleBackgroundClick = (e: React.MouseEvent) => {
    if (!backgroundRef.current) return
    
    const ripple = document.createElement('div')
    ripple.style.position = 'absolute'
    ripple.style.borderRadius = '50%'
    ripple.style.background = 'rgba(102, 126, 234, 0.3)'
    ripple.style.animation = `${rippleAnimation} 1s ease-out forwards`
    ripple.style.pointerEvents = 'none'
    
    const rect = backgroundRef.current.getBoundingClientRect()
    const size = 100
    const x = e.clientX - rect.left - size / 2
    const y = e.clientY - rect.top - size / 2
    
    ripple.style.width = ripple.style.height = size + 'px'
    ripple.style.left = x + 'px'
    ripple.style.top = y + 'px'
    
    backgroundRef.current.appendChild(ripple)
    
    setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple)
      }
    }, 1000)
  }

  // Floating shapes interaction
  const handleShapeClick = (e: React.MouseEvent) => {
    e.stopPropagation()

    if (!backgroundRef.current) return

    // Create burst effect
    for (let i = 0; i < 8; i++) {
      setTimeout(() => {
        const particle = document.createElement('div')
        particle.style.position = 'absolute'
        particle.style.width = '8px'
        particle.style.height = '8px'
        particle.style.background = '#667eea'
        particle.style.borderRadius = '50%'
        particle.style.zIndex = '15'

        // Get relative position within the background container
        const rect = backgroundRef.current!.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top

        particle.style.left = x + 'px'
        particle.style.top = y + 'px'
        particle.style.transform = `translate(-50%, -50%)`
        particle.style.animation = `${particleBurst} 0.8s ease-out forwards`

        // Random direction
        const angle = (i * 45) * Math.PI / 180
        const distance = 50
        particle.style.setProperty('--end-x', Math.cos(angle) * distance + 'px')
        particle.style.setProperty('--end-y', Math.sin(angle) * distance + 'px')

        backgroundRef.current!.appendChild(particle)

        setTimeout(() => {
          if (particle.parentNode) {
            particle.parentNode.removeChild(particle)
          }
        }, 800)
      }, i * 50)
    }
  }

  return (
    <Box
      ref={backgroundRef}
      onClick={handleBackgroundClick}
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        backgroundColor: '#FAFAFA',
      }}
    >
      {/* Floating Shapes */}
      <Box
        onClick={handleShapeClick}
        sx={{
          position: 'absolute',
          width: 80,
          height: 80,
          top: '20%',
          left: '10%',
          borderRadius: '50%',
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          opacity: 0.1,
          animation: `${float} 6s ease-in-out infinite`,
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            opacity: 0.3,
            transform: 'scale(1.2)',
          },
        }}
      />
      <Box
        onClick={handleShapeClick}
        sx={{
          position: 'absolute',
          width: 120,
          height: 120,
          top: '50%',
          right: '15%',
          borderRadius: '50%',
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          opacity: 0.1,
          animation: `${float} 6s ease-in-out infinite`,
          animationDelay: '-2s',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            opacity: 0.3,
            transform: 'scale(1.2)',
          },
        }}
      />
      <Box
        onClick={handleShapeClick}
        sx={{
          position: 'absolute',
          width: 60,
          height: 60,
          bottom: '30%',
          left: '20%',
          borderRadius: '50%',
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          opacity: 0.1,
          animation: `${float} 6s ease-in-out infinite`,
          animationDelay: '-4s',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            opacity: 0.3,
            transform: 'scale(1.2)',
          },
        }}
      />
      <Box
        onClick={handleShapeClick}
        sx={{
          position: 'absolute',
          width: 100,
          height: 100,
          top: '10%',
          right: '30%',
          borderRadius: '50%',
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          opacity: 0.1,
          animation: `${float} 6s ease-in-out infinite`,
          animationDelay: '-1s',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            opacity: 0.3,
            transform: 'scale(1.2)',
          },
        }}
      />
      <Box
        onClick={handleShapeClick}
        sx={{
          position: 'absolute',
          width: 90,
          height: 90,
          bottom: '20%',
          right: '40%',
          borderRadius: '50%',
          background: 'linear-gradient(45deg, #667eea, #764ba2)',
          opacity: 0.1,
          animation: `${float} 6s ease-in-out infinite`,
          animationDelay: '-3s',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            opacity: 0.3,
            transform: 'scale(1.2)',
          },
        }}
      />

      {/* Geometric Shapes */}
      <Box
        sx={{
          position: 'absolute',
          top: '15%',
          left: '25%',
          width: 0,
          height: 0,
          borderLeft: '15px solid transparent',
          borderRight: '15px solid transparent',
          borderBottom: '25px solid rgba(102, 126, 234, 0.08)',
          animation: `${rotate} 8s linear infinite`,
          pointerEvents: 'none',
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          top: '60%',
          right: '20%',
          width: 0,
          height: 0,
          borderLeft: '15px solid transparent',
          borderRight: '15px solid transparent',
          borderBottom: '25px solid rgba(102, 126, 234, 0.08)',
          animation: `${rotate} 8s linear infinite`,
          animationDelay: '-3s',
          pointerEvents: 'none',
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: '25%',
          left: '35%',
          width: 0,
          height: 0,
          borderLeft: '15px solid transparent',
          borderRight: '15px solid transparent',
          borderBottom: '25px solid rgba(102, 126, 234, 0.08)',
          animation: `${rotate} 8s linear infinite`,
          animationDelay: '-6s',
          pointerEvents: 'none',
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          top: '30%',
          right: '10%',
          width: 20,
          height: 20,
          background: 'rgba(118, 75, 162, 0.08)',
          animation: `${bounce} 4s ease-in-out infinite`,
          animationDelay: '-1s',
          pointerEvents: 'none',
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: '40%',
          left: '15%',
          width: 20,
          height: 20,
          background: 'rgba(118, 75, 162, 0.08)',
          animation: `${bounce} 4s ease-in-out infinite`,
          animationDelay: '-2s',
          pointerEvents: 'none',
        }}
      />

      {/* Wave Effect */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          width: '100%',
          height: 100,
          background: 'linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1))',
          clipPath: 'polygon(0 20px, 100% 0, 100% 100%, 0 100%)',
          animation: `${waveAnimation} 3s ease-in-out infinite`,
          pointerEvents: 'none',
        }}
      />
    </Box>
  )
}
