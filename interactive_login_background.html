<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Login - İnteraktif Background</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
            background: #FAFAFA;
            position: relative;
        }

        .background-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #FAFAFA;
            overflow: hidden;
        }

        .floating-shape {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .floating-shape:hover {
            opacity: 0.3;
            transform: scale(1.2);
        }

        .floating-shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 50%;
            right: 15%;
            animation-delay: -2s;
        }

        .floating-shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 30%;
            left: 20%;
            animation-delay: -4s;
        }

        .floating-shape:nth-child(4) {
            width: 100px;
            height: 100px;
            top: 10%;
            right: 30%;
            animation-delay: -1s;
        }

        .floating-shape:nth-child(5) {
            width: 90px;
            height: 90px;
            bottom: 20%;
            right: 40%;
            animation-delay: -3s;
        }

        .geometric-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .triangle {
            position: absolute;
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-bottom: 25px solid rgba(102, 126, 234, 0.08);
            animation: rotate 8s linear infinite;
        }

        .triangle:nth-child(1) {
            top: 15%;
            left: 25%;
            animation-delay: 0s;
        }

        .triangle:nth-child(2) {
            top: 60%;
            right: 20%;
            animation-delay: -3s;
        }

        .triangle:nth-child(3) {
            bottom: 25%;
            left: 35%;
            animation-delay: -6s;
        }

        .square {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(118, 75, 162, 0.08);
            animation: bounce 4s ease-in-out infinite;
        }

        .square:nth-child(4) {
            top: 30%;
            right: 10%;
            animation-delay: -1s;
        }

        .square:nth-child(5) {
            bottom: 40%;
            left: 15%;
            animation-delay: -2s;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            animation: particle-move 10s linear infinite;
        }



        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(102, 126, 234, 0.3);
            animation: ripple-animation 1s ease-out forwards;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }

        @keyframes particle-move {
            0% { transform: translateY(100vh) translateX(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100vh) translateX(100px); opacity: 0; }
        }

        @keyframes ripple-animation {
            0% {
                transform: scale(0);
                opacity: 0.6;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }

        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            clip-path: polygon(0 20px, 100% 0, 100% 100%, 0 100%);
            animation: wave-animation 3s ease-in-out infinite;
        }

        .mini-game {
            position: absolute;
            right: 50px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            width: 280px;
            text-align: center;
            z-index: 5;
        }

        .game-title {
            font-size: 18px;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 15px;
        }

        .game-area {
            position: relative;
            height: 200px;
            background: linear-gradient(135deg, #f8f9ff 0%, #e6eaff 100%);
            border-radius: 12px;
            overflow: hidden;
            border: 2px solid rgba(102, 126, 234, 0.1);
            margin-bottom: 15px;
        }

        .game-ball {
            position: absolute;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .game-ball:hover {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .game-score {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .game-instruction {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }

        .reset-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .reset-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .game-target {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 2px solid #667eea;
            border-radius: 50%;
            animation: pulse 2s infinite;
            pointer-events: none;
        }

        .game-target::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.1); opacity: 1; }
            100% { transform: scale(1); opacity: 0.7; }
        }

        @media (max-width: 1024px) {
            .mini-game {
                right: 20px;
                width: 240px;
            }
        }

        @keyframes wave-animation {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(-10px); }
        }

        @media (max-width: 1024px) {
            .mini-game {
                right: 20px;
                width: 240px;
            }
        }

        @media (max-width: 768px) {
            .mini-game {
                bottom: 20px;
                right: 20px;
                top: auto;
                transform: none;
                width: 200px;
            }
        }


    </style>
</head>
<body>
    <div class="background-container">
        <!-- Floating shapes -->
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        
        <!-- Geometric shapes -->
        <div class="geometric-shapes">
            <div class="triangle"></div>
            <div class="triangle"></div>
            <div class="triangle"></div>
            <div class="square"></div>
            <div class="square"></div>
        </div>
        
        <!-- Wave effect -->
        <div class="wave"></div>
    </div>
    
    <div class="login-container">
        <div class="logo">KRAL</div>
        <div class="subtitle">Hoşgeldiniz</div>
        
        <div class="input-group">
            <input type="text" class="input-field" placeholder="Kullanıcı Adı">
        </div>
        
        <div class="input-group">
            <input type="password" class="input-field" placeholder="Şifre">
        </div>
        
        <button class="login-btn">Giriş Yap</button>
    </div>

    <script>
        // Interactive ripple effect
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('floating-shape')) {
                return;
            }
            
            const ripple = document.createElement('div');
            ripple.classList.add('ripple');
            
            const rect = document.body.getBoundingClientRect();
            const size = 100;
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            
            document.body.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 1000);
        });

        // Floating shapes interaction
        document.querySelectorAll('.floating-shape').forEach(shape => {
            shape.addEventListener('click', function(e) {
                e.stopPropagation();
                
                // Create burst effect
                for (let i = 0; i < 8; i++) {
                    setTimeout(() => {
                        const particle = document.createElement('div');
                        particle.style.position = 'absolute';
                        particle.style.width = '8px';
                        particle.style.height = '8px';
                        particle.style.background = '#667eea';
                        particle.style.borderRadius = '50%';
                        particle.style.left = e.clientX + 'px';
                        particle.style.top = e.clientY + 'px';
                        particle.style.transform = `translate(-50%, -50%)`;
                        particle.style.animation = `particle-burst 0.8s ease-out forwards`;
                        
                        // Random direction
                        const angle = (i * 45) * Math.PI / 180;
                        const distance = 50;
                        particle.style.setProperty('--end-x', Math.cos(angle) * distance + 'px');
                        particle.style.setProperty('--end-y', Math.sin(angle) * distance + 'px');
                        
                        document.body.appendChild(particle);
                        
                        setTimeout(() => particle.remove(), 800);
                    }, i * 50);
                }
            });
        });

        // Add particle burst animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes particle-burst {
                0% {
                    transform: translate(-50%, -50%) scale(1);
                    opacity: 1;
                }
                100% {
                    transform: translate(calc(-50% + var(--end-x)), calc(-50% + var(--end-y))) scale(0);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Generate random particles
        function createParticle() {
            const particle = document.createElement('div');
            particle.classList.add('particle');
            particle.style.left = Math.random() * 100 + 'vw';
            particle.style.animationDelay = Math.random() * 10 + 's';
            particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
            
            document.querySelector('.background-container').appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 20000);
        }

        // Create particles periodically
        setInterval(createParticle, 2000);


    </script>
</body>
</html>