import jwt, { SignOptions } from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import { PrismaClient } from '@prisma/client'
// Temporary local types until shared types are properly configured
interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

const prisma = new PrismaClient()

interface TokenPayload {
  userId: string
  username: string
  role: string
  companyId: string
  branchId: string
}

interface LoginRequest {
  username: string
  password: string
}

interface LoginResponse {
  user: {
    id: string
    username: string
    firstName: string
    lastName: string
    email?: string
    role: string
    companyId: string
    branchId: string
  }
  token: string
}

export class AuthService {
  private readonly JWT_SECRET: string = process.env.JWT_SECRET || 'your-secret-key'
  private readonly JWT_EXPIRE: string = process.env.JWT_EXPIRE || '12h' // Vardiya süresi

  /**
   * <PERSON>llanı<PERSON><PERSON> girişi
   */
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    try {
      // Kullanıcıyı bul
      const user = await prisma.user.findUnique({
        where: { 
          username: credentials.username,
          active: true 
        },
        include: {
          company: true,
          branch: true
        }
      })

      if (!user) {
        return {
          success: false,
          error: 'Kullanıcı adı veya şifre hatalı'
        }
      }

      // Şifre kontrolü
      const isPasswordValid = await bcrypt.compare(credentials.password, user.password)
      if (!isPasswordValid) {
        return {
          success: false,
          error: 'Kullanıcı adı veya şifre hatalı'
        }
      }

      // Token oluştur
      const tokenPayload: TokenPayload = {
        userId: user.id,
        username: user.username,
        role: user.role,
        companyId: user.companyId,
        branchId: user.branchId || '' // null kontrolü
      }

      const token = this.generateToken(tokenPayload)

      // Son giriş zamanını güncelle
      await prisma.user.update({
        where: { id: user.id },
        data: {
          lastLoginAt: new Date()
        }
      })

      const response: LoginResponse = {
        user: {
          id: user.id,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email || undefined,
          role: user.role,
          companyId: user.companyId,
          branchId: user.branchId || ''
        },
        token
      }

      return {
        success: true,
        data: response,
        message: 'Giriş başarılı'
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        error: 'Giriş sırasında bir hata oluştu'
      }
    }
  }

  /**
   * Token doğrulama
   */
  verifyToken(token: string): TokenPayload | null {
    try {
      return jwt.verify(token, this.JWT_SECRET) as TokenPayload
    } catch {
      return null
    }
  }

  /**
   * Çıkış (Basit - sadece log)
   */
  async logout(userId: string): Promise<ApiResponse<null>> {
    try {
      // Sadece son çıkış zamanını logla
      await prisma.user.update({
        where: { id: userId },
        data: { lastLoginAt: new Date() } // Son aktivite zamanı
      })

      return {
        success: true,
        message: 'Çıkış başarılı'
      }
    } catch {
      return {
        success: false,
        error: 'Çıkış sırasında bir hata oluştu'
      }
    }
  }

  /**
   * Token oluştur
   */
  private generateToken(payload: TokenPayload): string {
    const options: SignOptions = { expiresIn: this.JWT_EXPIRE }
    return jwt.sign(payload, this.JWT_SECRET, options)
  }
}

export const authService = new AuthService()
